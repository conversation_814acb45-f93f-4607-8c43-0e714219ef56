# 历史数据去重测试示例

## 测试目的
验证 `HistoricalYearData` 类的去重逻辑是否正常工作。

## 测试代码示例

```java
import java.util.*;

public class HistoricalDataDeduplicationTest {
    
    public static void main(String[] args) {
        testDeduplication();
    }
    
    public static void testDeduplication() {
        // 创建测试数据，包含重复项
        List<MajorAdmissionInfo.HistoricalYearData> testData = createTestData();
        
        System.out.println("原始数据数量: " + testData.size());
        printData("原始数据", testData);
        
        // 执行去重
        List<MajorAdmissionInfo.HistoricalYearData> deduplicatedData = deduplicateData(testData);
        
        System.out.println("\n去重后数据数量: " + deduplicatedData.size());
        printData("去重后数据", deduplicatedData);
    }
    
    private static List<MajorAdmissionInfo.HistoricalYearData> createTestData() {
        List<MajorAdmissionInfo.HistoricalYearData> data = new ArrayList<>();
        
        // 添加第一条数据
        MajorAdmissionInfo.HistoricalYearData data1 = new MajorAdmissionInfo.HistoricalYearData();
        data1.setYear(2023);
        data1.setHighScore("-");
        data1.setAverageScore("-");
        data1.setLowestScore("547");
        data1.setLowestSection("5363");
        data.add(data1);
        
        // 添加重复数据
        MajorAdmissionInfo.HistoricalYearData data2 = new MajorAdmissionInfo.HistoricalYearData();
        data2.setYear(2023);
        data2.setHighScore("-");
        data2.setAverageScore("-");
        data2.setLowestScore("547");
        data2.setLowestSection("5363");
        data.add(data2);
        
        // 添加不同的数据
        MajorAdmissionInfo.HistoricalYearData data3 = new MajorAdmissionInfo.HistoricalYearData();
        data3.setYear(2022);
        data3.setHighScore("-");
        data3.setAverageScore("-");
        data3.setLowestScore("545");
        data3.setLowestSection("5500");
        data.add(data3);
        
        // 添加另一条重复数据
        MajorAdmissionInfo.HistoricalYearData data4 = new MajorAdmissionInfo.HistoricalYearData();
        data4.setYear(2023);
        data4.setHighScore("600");  // 注意：highScore不同，但不影响去重
        data4.setAverageScore("580"); // 注意：averageScore不同，但不影响去重
        data4.setLowestScore("547");
        data4.setLowestSection("5363");
        data.add(data4);
        
        return data;
    }
    
    private static List<MajorAdmissionInfo.HistoricalYearData> deduplicateData(
            List<MajorAdmissionInfo.HistoricalYearData> originalData) {
        
        Set<MajorAdmissionInfo.HistoricalYearData> uniqueDataSet = new HashSet<>();
        List<MajorAdmissionInfo.HistoricalYearData> deduplicatedData = new ArrayList<>();
        
        for (MajorAdmissionInfo.HistoricalYearData data : originalData) {
            if (uniqueDataSet.add(data)) {
                deduplicatedData.add(data);
                System.out.println("添加数据: " + formatData(data));
            } else {
                System.out.println("发现重复数据，已跳过: " + formatData(data));
            }
        }
        
        return deduplicatedData;
    }
    
    private static void printData(String title, List<MajorAdmissionInfo.HistoricalYearData> data) {
        System.out.println("\n" + title + ":");
        for (int i = 0; i < data.size(); i++) {
            System.out.println((i + 1) + ". " + formatData(data.get(i)));
        }
    }
    
    private static String formatData(MajorAdmissionInfo.HistoricalYearData data) {
        return String.format("年份=%d, 最高分=%s, 平均分=%s, 最低分=%s, 最低位次=%s",
                data.getYear(), data.getHighScore(), data.getAverageScore(), 
                data.getLowestScore(), data.getLowestSection());
    }
}
```

## 预期测试结果

```
原始数据数量: 4

原始数据:
1. 年份=2023, 最高分=-, 平均分=-, 最低分=547, 最低位次=5363
2. 年份=2023, 最高分=-, 平均分=-, 最低分=547, 最低位次=5363
3. 年份=2022, 最高分=-, 平均分=-, 最低分=545, 最低位次=5500
4. 年份=2023, 最高分=600, 平均分=580, 最低分=547, 最低位次=5363

添加数据: 年份=2023, 最高分=-, 平均分=-, 最低分=547, 最低位次=5363
发现重复数据，已跳过: 年份=2023, 最高分=-, 平均分=-, 最低分=547, 最低位次=5363
添加数据: 年份=2022, 最高分=-, 平均分=-, 最低分=545, 最低位次=5500
发现重复数据，已跳过: 年份=2023, 最高分=600, 平均分=580, 最低分=547, 最低位次=5363

去重后数据数量: 2

去重后数据:
1. 年份=2023, 最高分=-, 平均分=-, 最低分=547, 最低位次=5363
2. 年份=2022, 最高分=-, 平均分=-, 最低分=545, 最低位次=5500
```

## 关键点说明

1. **去重依据**：只基于年份、最低分、最低位次三个字段
2. **其他字段**：最高分和平均分的不同不会影响去重判断
3. **保留策略**：重复数据中保留第一条记录
4. **性能**：使用 HashSet 的 O(1) 查找性能，整体时间复杂度为 O(n)

## 实际应用

在 `batchQueryThreeYearsAdmissionScores` 方法中，这个去重逻辑确保了：
- 同一专业的同一年份不会有重复的录取分数记录
- 提高数据质量和用户体验
- 减少不必要的数据传输和存储
