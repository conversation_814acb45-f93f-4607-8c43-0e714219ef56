# 近三年专业录取分数查询功能说明

## 功能概述

为 `getAllSuitableMajors` 方法添加了近三年专业录取分数查询功能，使得每个推荐的专业都包含近三年（2022-2024）的录取分数历史数据。

## 修改内容

### 1. 主要修改文件
- `yudao-module-system/yudao-module-system-biz/src/main/java/cn/iocoder/yudao/module/system/service/gugu/UserProfileService.java`

### 2. 新增方法

#### 2.1 `batchQueryThreeYearsAdmissionScores` 方法
```java
private void batchQueryThreeYearsAdmissionScores(List<MajorAdmissionInfo> majors, UserProfileInfo profile)
```

**功能说明：**
- 批量查询近三年专业录取分数数据
- 使用异步并行查询提高性能
- 查询年份：2024、2023、2022
- 设置超时时间为30秒，避免长时间阻塞

**实现特点：**
- 使用 `CompletableFuture` 实现异步并行查询
- 使用现有的线程池 `executorService`
- 为每个专业查询近三年的录取分数数据
- 自动去重：使用 `HashSet` 去除重复的历史数据记录
- 将查询结果存储到 `MajorAdmissionInfo` 的 `historicalData` 字段

#### 2.2 `queryAdmissionScoreForMajorAndYear` 方法
```java
private List<MajorAdmissionInfo.HistoricalYearData> queryAdmissionScoreForMajorAndYear(MajorAdmissionInfo major, UserProfileInfo profile, Integer year)
```

**功能说明：**
- 查询指定专业和年份的录取分数数据
- 调用现有的 `majorAdmissionService.getMajorAdmissionBySchoolMajorYearTypeNameprovinceName` 方法
- 将查询结果转换为 `HistoricalYearData` 对象

**查询参数：**
- `schoolUUID`: 学校唯一标识
- `majorName`: 专业名称
- `year`: 查询年份
- `typeName`: 科目类型（物理类/历史类等）
- `provinceName`: 省份名称

### 3. 修改的方法

#### 3.1 `getAllSuitableMajors` 方法
在原有的招生计划查询后，添加了录取分数查询：

```java
// 为最终分类结果查询近三年招生计划数据
batchQueryThreeYearsEnrollmentPlans(rushMajors, profile);
batchQueryThreeYearsEnrollmentPlans(stableMajors, profile);
batchQueryThreeYearsEnrollmentPlans(safeMajors, profile);

// 为最终分类结果查询近三年专业录取分数数据
batchQueryThreeYearsAdmissionScores(rushMajors, profile);
batchQueryThreeYearsAdmissionScores(stableMajors, profile);
batchQueryThreeYearsAdmissionScores(safeMajors, profile);
```

#### 3.2 `buildQueryInfoForAllSuitableWithClassification` 方法
更新了查询信息说明，添加了录取分数数据查询的说明：

```java
queryInfo.append("• 已查询近三年（2022-2024）招生计划数据\n");
queryInfo.append("• 已查询近三年（2022-2024）专业录取分数数据\n\n");
```

## 技术实现

### 1. 异步并行查询
- 使用 `CompletableFuture.runAsync()` 实现异步查询
- 使用现有的线程池 `executorService`（固定10个线程）
- 为每个专业创建独立的查询任务

### 2. 数据转换
- 复用现有的 `convertToHistoricalYearData` 方法
- 将 `MajorAdmissionDO` 转换为 `MajorAdmissionInfo.HistoricalYearData`

### 3. 错误处理
- 每个查询任务都有独立的异常处理
- 查询失败不会影响其他专业的查询
- 记录详细的日志信息

### 4. 数据去重
- 使用 `HashSet<HistoricalYearData>` 进行自动去重
- 去重依据：年份、最低分、最低位次三个字段的组合
- `HistoricalYearData` 类已实现 `equals()` 和 `hashCode()` 方法
- 重复数据会被自动过滤，只保留第一条记录
- 记录调试日志显示被过滤的重复数据

### 5. 性能优化
- 设置30秒超时时间，避免长时间等待
- 使用并行查询，提高整体查询效率
- 查询失败时记录警告日志但不中断整体流程
- 去重处理在内存中进行，不影响数据库查询性能

## 数据结构

### HistoricalYearData 字段
- `year`: 年份
- `highScore`: 最高分
- `averageScore`: 平均分
- `lowestScore`: 最低分
- `lowestSection`: 最低位次

## 使用场景

### 1. 冲稳保分类
为每个分类（冲刺、稳妥、保底）的专业都查询近三年录取分数数据。

### 2. 数据完整性
确保推荐的专业既有招生计划数据，也有历史录取分数数据，为用户提供更全面的参考信息。

### 3. 趋势分析
用户可以通过近三年的录取分数数据，分析专业录取分数的变化趋势。

## 日志记录

### 1. 信息日志
- 开始查询时记录专业数量
- 完成查询时记录总耗时
- 成功查询时记录数据条数

### 2. 调试日志
- 每个专业的查询结果
- 查询到的数据条数

### 3. 警告日志
- 查询超时警告
- 单个专业查询失败警告

## 兼容性

### 1. 向后兼容
- 不影响现有的招生计划查询功能
- 录取分数查询失败不会影响整体流程

### 2. 数据依赖
- 依赖现有的 `MajorAdmissionService` 服务
- 使用现有的数据库查询方法

## 去重逻辑详解

### 问题背景
在查询历史录取分数数据时，可能会出现重复记录，例如：
```json
{
    "year": 2023,
    "highScore": "-",
    "averageScore": "-",
    "lowestScore": "547",
    "lowestSection": "5363"
},
{
    "year": 2023,
    "highScore": "-",
    "averageScore": "-",
    "lowestScore": "547",
    "lowestSection": "5363"
}
```

### 解决方案
1. **去重依据**：基于年份、最低分、最低位次三个关键字段
2. **实现方式**：使用 `HashSet<HistoricalYearData>` 自动去重
3. **equals/hashCode**：`HistoricalYearData` 类已正确实现这两个方法
4. **处理流程**：
   - 创建 `HashSet` 用于检测重复
   - 创建 `ArrayList` 存储去重后的数据
   - 遍历查询结果，使用 `Set.add()` 方法检测重复
   - 只有非重复数据才添加到最终结果列表

### 日志记录
- 重复数据会记录调试日志：`发现重复的录取分数数据，已跳过`
- 最终结果会显示去重后的数据条数

## 总结

通过添加近三年专业录取分数查询功能，`getAllSuitableMajors` 方法现在能够为用户提供更加完整和详细的专业信息，包括：

1. **招生计划数据**：近三年的招生计划信息
2. **录取分数数据**：近三年的录取分数历史数据（已去重）
3. **冲稳保分类**：根据用户分数进行专业分类
4. **异步查询**：高效的并行查询机制
5. **数据质量**：自动去重确保数据的准确性

这些改进使得专业推荐功能更加完善，为用户的志愿填报决策提供了更全面、更准确的数据支持。
