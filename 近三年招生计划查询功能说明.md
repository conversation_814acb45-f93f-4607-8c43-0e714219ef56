# 近三年招生计划查询功能说明

## 功能概述

为冲稳保分类的专业数据增加了近三年（2022-2024）招生计划查询功能，为用户提供更全面的历史招生数据，帮助用户更好地了解专业的招生情况和趋势。

## 核心实现

### 1. 异步并行查询
```java
private void batchQueryThreeYearsEnrollmentPlans(List<MajorAdmissionInfo> majors, UserProfileInfo profile) {
    // 定义查询的年份（近三年）
    List<Integer> years = Arrays.asList(2024, 2023, 2022);
    
    // 使用并行流提高查询效率
    List<CompletableFuture<Void>> futures = majors.stream()
            .map(major -> CompletableFuture.runAsync(() -> {
                // 查询逻辑
            }, executorService))
            .collect(Collectors.toList());
    
    // 等待所有查询完成，设置超时时间为30秒
    CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    allFutures.get(30, TimeUnit.SECONDS);
}
```

### 2. 单年度查询方法
```java
private List<CollegeEnrollmentPlanInfo> queryEnrollmentPlanForMajorAndYear(
        MajorAdmissionInfo major, UserProfileInfo profile, Integer year) {
    return collegeEnrollmentPlanService.getEnrollmentPlanForMajor(
            major.getSchoolName(),
            major.getMajorName(),
            profile.getProvince(),
            profile.getTypeName(),
            year
    );
}
```

## 查询流程

### 1. 分类后查询
- 先进行冲稳保分类
- 对每个分类的专业分别查询招生计划
- 确保只为最终结果查询数据，避免无效查询

### 2. 多年度数据整合
- 查询2024年、2023年、2022年三年数据
- 将多年数据合并到同一个专业的 `enrollmentPlanData` 字段
- 保持数据的完整性和一致性

### 3. 异步处理
- 使用线程池进行并行查询
- 每个专业的查询独立进行，互不影响
- 设置合理的超时时间，避免长时间等待

## 性能优化

### 1. 缓存利用
- 利用现有的 `@Cacheable` 缓存机制
- 相同查询参数的数据直接从缓存获取
- 减少重复的API调用

### 2. 并发控制
- 使用固定大小的线程池（10个线程）
- 避免创建过多线程导致系统负载过高
- 合理的超时设置（30秒）

### 3. 错误处理
- 单个专业查询失败不影响其他专业
- 详细的日志记录，便于问题排查
- 优雅的降级处理

## 数据结构

### 招生计划数据字段
```json
{
  "enrollmentPlanData": [
    {
      "year": 2024,
      "schoolName": "中国医科大学",
      "collegeMajorName": "护理学",
      "provinceName": "辽宁",
      "type": "历史类",
      "enrollmentNumbers": "30",
      "batchName": "本科批",
      "courseSelectionRequirements": "历史+生物",
      "classOne": "医学",
      "classTwo": "护理学类"
    },
    {
      "year": 2023,
      // ... 2023年数据
    },
    {
      "year": 2022,
      // ... 2022年数据
    }
  ]
}
```

## 使用场景

### 1. 趋势分析
- 查看专业近三年的招生人数变化
- 了解专业的热门程度趋势
- 分析录取要求的变化

### 2. 风险评估
- 对比历年招生计划，评估录取难度
- 了解专业的稳定性
- 为志愿填报提供数据支撑

### 3. 详细了解
- 查看专业的具体招生要求
- 了解选科要求的变化
- 获取完整的专业信息

## 日志监控

### 1. 查询统计
```
开始为 5 个专业异步查询近三年招生计划数据
专业 中国医科大学 - 护理学 查询到 3 条近三年招生计划数据
完成 5 个专业的近三年招生计划数据查询
```

### 2. 性能监控
- 记录查询开始和结束时间
- 统计成功和失败的查询数量
- 监控超时情况

### 3. 错误处理
```
查询专业 某大学 - 某专业 在 2022 年的招生计划数据失败: 网络超时
招生计划数据查询超时，部分数据可能未完成查询
```

## 配置说明

### 1. 查询年份
- 当前配置：2024, 2023, 2022
- 可根据需要调整年份范围
- 建议保持3年的数据量

### 2. 超时设置
- 当前设置：30秒
- 可根据网络环境调整
- 平衡响应速度和数据完整性

### 3. 线程池配置
- 当前配置：10个线程
- 可根据服务器性能调整
- 避免过多并发请求

## 注意事项

### 1. 数据可用性
- 部分专业可能没有完整的三年数据
- 新增专业可能只有近一两年的数据
- 查询失败不影响专业的基本信息展示

### 2. 性能影响
- 增加了查询时间，但通过异步处理最小化影响
- 缓存机制减少重复查询的开销
- 超时机制避免长时间等待

### 3. 数据一致性
- 确保查询参数的一致性（省份、类型等）
- 处理数据格式的差异
- 保持数据的时效性

## 后续优化建议

### 1. 智能缓存
- 实现更智能的缓存策略
- 预加载热门专业的数据
- 定期更新缓存数据

### 2. 数据预处理
- 批量预查询热门专业数据
- 建立本地数据库缓存
- 减少实时查询的压力

### 3. 用户体验
- 提供数据加载进度提示
- 支持部分数据的渐进式展示
- 优化超时和错误提示

## 总结

近三年招生计划查询功能为用户提供了更丰富的历史数据，帮助用户更好地了解专业的招生趋势和要求。通过异步并行查询和缓存机制，在保证数据完整性的同时，最大程度地优化了性能表现。
