package cn.iocoder.yudao.module.system.service.gugu;

import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeEnrollmentPlanInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeEnrollmentPlanQueryReqVO;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.MajorAdmissionInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.MajorAdmissionQueryReqVO;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.UserProfileInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.vo.StructuredUserDataReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.gugu.MajorAdmissionDO;
import cn.iocoder.yudao.module.system.util.ChineseSegmentationUtil;
import cn.iocoder.yudao.module.system.util.SubjectSelectionUtils;
import cn.iocoder.yudao.module.system.util.gugu.GuGuDataUtils;
import org.springframework.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;

import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 用户个人信息服务
 */
@Service
@EnableAsync
@Slf4j
public class UserProfileService {

    // 创建线程池，用于并行查询历史数据
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Resource
    private MajorAdmissionService majorAdmissionService;

    @Resource
    private CollegeEnrollmentPlanService collegeEnrollmentPlanService;

    /**
     * 在服务关闭时关闭线程池
     */
    @PreDestroy
    public void shutdown() {
        // 尝试优雅地关闭线程池
        executorService.shutdown();
        try {
            // 等待所有任务完成，最多等待5秒
            if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                // 如果超时，强制关闭
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            // 如果等待被中断，强制关闭
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 从用户输入中提取个人信息
     * @param userInput 用户输入的文本
     * @return 提取的用户个人信息
     */
    public UserProfileInfo extractUserProfile(String userInput) {
        UserProfileInfo profile = new UserProfileInfo();

        // 提取省份
        Pattern provincePattern = Pattern.compile("([\u4e00-\u9fa5]+)(省|市|自治区)?\\d{4}年考生");
        Matcher provinceMatcher = provincePattern.matcher(userInput);
        if (provinceMatcher.find()) {
            profile.setProvince(provinceMatcher.group(1));
        }

        // 提取年份
        Pattern yearPattern = Pattern.compile("(\\d{4})年考生");
        Matcher yearMatcher = yearPattern.matcher(userInput);
        if (yearMatcher.find()) {
            profile.setYear(Integer.parseInt(yearMatcher.group(1)));
        }

        // 提取性别
        if (userInput.contains("男生")) {
            profile.setGender("男");
        } else if (userInput.contains("女生")) {
            profile.setGender("女");
        }

        // 提取选科
        List<String> subjects = new ArrayList<>();
        Pattern subjectPattern = Pattern.compile("选科是([^，。]+)");
        Matcher subjectMatcher = subjectPattern.matcher(userInput);
        if (subjectMatcher.find()) {
            String subjectsStr = subjectMatcher.group(1);
            String[] subjectArray = subjectsStr.split("、|，|,|\\s+");
            subjects.addAll(Arrays.asList(subjectArray));
        }
        profile.setSubjects(subjects);

        // 提取总分
        Pattern totalScorePattern = Pattern.compile("高考总分(\\d+)");
        Matcher totalScoreMatcher = totalScorePattern.matcher(userInput);
        if (totalScoreMatcher.find()) {
            profile.setTotalScore(Integer.parseInt(totalScoreMatcher.group(1)));
        }

        // 提取各科分数
        Map<String, Integer> subjectScores = new HashMap<>();
        Pattern subjectScorePattern = Pattern.compile("([^:：,，\\.。\\d]+):?：?(\\d+)");
        Matcher subjectScoreMatcher = subjectScorePattern.matcher(userInput);
        while (subjectScoreMatcher.find()) {
            String subject = subjectScoreMatcher.group(1).trim();
            Integer score = Integer.parseInt(subjectScoreMatcher.group(2));
            subjectScores.put(subject, score);
        }
        profile.setSubjectScores(subjectScores);

        // 提取性格特点
        List<String> personalityTraits = new ArrayList<>();
        if (userInput.contains("性格内向")) {
            personalityTraits.add("内向");
        }
        if (userInput.contains("性格外向")) {
            personalityTraits.add("外向");
        }
        if (userInput.contains("学习能力强")) {
            personalityTraits.add("学习能力强");
        }
        if (userInput.contains("社交能力强")) {
            personalityTraits.add("社交能力强");
        }
        profile.setPersonalityTraits(personalityTraits);

        // 提取家庭年收入
        Pattern incomePattern = Pattern.compile("家庭年收入(\\d+)万到(\\d+)万");
        Matcher incomeMatcher = incomePattern.matcher(userInput);
        if (incomeMatcher.find()) {
            String lowerBound = incomeMatcher.group(1);
            String upperBound = incomeMatcher.group(2);
            profile.setFamilyIncome(lowerBound + "万-" + upperBound + "万");
        }

        // 提取毕业去向
        if (userInput.contains("大学毕业后会就业")) {
            profile.setGraduationPlan("就业");
        } else if (userInput.contains("大学毕业后会考研")) {
            profile.setGraduationPlan("考研");
        } else if (userInput.contains("大学毕业后会出国")) {
            profile.setGraduationPlan("出国");
        }

        // 提取感兴趣的专业类别
        List<String> interestedMajors = new ArrayList<>();
        Pattern majorPattern = Pattern.compile("对([^,，。]+)感兴趣");
        Matcher majorMatcher = majorPattern.matcher(userInput);
        if (majorMatcher.find()) {
            String majorsStr = majorMatcher.group(1);
            String[] majorArray = majorsStr.split("、|，|,|\\s+");
            interestedMajors.addAll(Arrays.asList(majorArray));
        }
        profile.setInterestedMajorCategories(interestedMajors);

        // 提取期望学校所在地
        Pattern locationPattern = Pattern.compile("想去的学校所在地：([^，。]+)");
        Matcher locationMatcher = locationPattern.matcher(userInput);
        if (locationMatcher.find()) {
            profile.setPreferredLocation(locationMatcher.group(1));
        }

        // 根据选科确定用户类型
        if (profile.getSubjects() != null && !profile.getSubjects().isEmpty()) {
            // 检查是否包含物理和历史，这两个是首选科目
            boolean hasPhysics = profile.getSubjects().stream().anyMatch(s -> s.contains("物理"));
            boolean hasHistory = profile.getSubjects().stream().anyMatch(s -> s.contains("历史"));

            String firstSubject = null;
            if (hasPhysics) {
                firstSubject = "物理";
            } else if (hasHistory) {
                firstSubject = "历史";
            }

            if (firstSubject != null) {
                String typeName = determineSubjectSelection(profile.getProvince(), firstSubject);
                profile.setTypeName(typeName);
                log.info("根据用户选科确定类型为: {}", typeName);
            }
        }

        return profile;
    }

    /**
     * 从结构化用户数据中提取个人信息
     * @param userAnswers 结构化的用户问答数据
     * @return 提取的用户个人信息
     */
    public UserProfileInfo extractUserProfileFromStructuredData(Map<String, StructuredUserDataReqVO.QuestionAnswer> userAnswers) {
        UserProfileInfo profile = new UserProfileInfo();

        try {
            // 1. 提取省份
            StructuredUserDataReqVO.QuestionAnswer provinceAnswer = userAnswers.get("1");
            if (provinceAnswer != null && provinceAnswer.getAnswer() != null) {
                profile.setProvince(provinceAnswer.getAnswer().toString());
            }

            // 2. 提取性别
            StructuredUserDataReqVO.QuestionAnswer genderAnswer = userAnswers.get("2");
            if (genderAnswer != null && genderAnswer.getAnswer() != null) {
                String genderStr = genderAnswer.getAnswer().toString();
                if (genderStr.contains("女")) {
                    profile.setGender("女");
                } else if (genderStr.contains("男")) {
                    profile.setGender("男");
                }
            }

            // 3. 提取选科
            StructuredUserDataReqVO.QuestionAnswer subjectsAnswer = userAnswers.get("3");
            if (subjectsAnswer != null && subjectsAnswer.getAnswer() != null) {
                List<String> subjects = new ArrayList<>();
                if (subjectsAnswer.getAnswer() instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> subjectList = (List<String>) subjectsAnswer.getAnswer();
                    subjects.addAll(subjectList);
                }
                profile.setSubjects(subjects);
            }

            // 5. 提取总分
            StructuredUserDataReqVO.QuestionAnswer totalScoreAnswer = userAnswers.get("5");
            if (totalScoreAnswer != null && totalScoreAnswer.getAnswer() != null) {
                try {
                    profile.setTotalScore(Integer.parseInt(totalScoreAnswer.getAnswer().toString()));
                } catch (NumberFormatException e) {
                    log.warn("解析总分失败: {}", totalScoreAnswer.getAnswer());
                }
            }

            // 6. 提取各科分数
            StructuredUserDataReqVO.QuestionAnswer subjectScoresAnswer = userAnswers.get("6");
            if (subjectScoresAnswer != null && subjectScoresAnswer.getAnswer() != null) {
                Map<String, Integer> subjectScores = parseSubjectScores(subjectScoresAnswer.getAnswer().toString());
                profile.setSubjectScores(subjectScores);
            }

            // 7. 提取意向专业
            StructuredUserDataReqVO.QuestionAnswer interestedMajorsAnswer = userAnswers.get("7");
            if (interestedMajorsAnswer != null && interestedMajorsAnswer.getAnswer() != null) {
                List<String> interestedMajors = new ArrayList<>();
                if (interestedMajorsAnswer.getAnswer() instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> majorList = (List<String>) interestedMajorsAnswer.getAnswer();
                    interestedMajors.addAll(majorList);
                }
                profile.setInterestedMajorCategories(interestedMajors);
            }

            // 8-10. 提取性格特点
            List<String> personalityTraits = new ArrayList<>();

            StructuredUserDataReqVO.QuestionAnswer personalityAnswer = userAnswers.get("8");
            if (personalityAnswer != null && personalityAnswer.getAnswer() != null) {
                personalityTraits.add(personalityAnswer.getAnswer().toString());
            }

            StructuredUserDataReqVO.QuestionAnswer learningAbilityAnswer = userAnswers.get("9");
            if (learningAbilityAnswer != null && learningAbilityAnswer.getAnswer() != null) {
                personalityTraits.add("学习能力" + learningAbilityAnswer.getAnswer().toString());
            }

            StructuredUserDataReqVO.QuestionAnswer socialAbilityAnswer = userAnswers.get("10");
            if (socialAbilityAnswer != null && socialAbilityAnswer.getAnswer() != null) {
                personalityTraits.add("社交能力" + socialAbilityAnswer.getAnswer().toString());
            }

            profile.setPersonalityTraits(personalityTraits);

            // 11. 提取家庭年收入
            StructuredUserDataReqVO.QuestionAnswer incomeAnswer = userAnswers.get("11");
            if (incomeAnswer != null && incomeAnswer.getAnswer() != null) {
                profile.setFamilyIncome(incomeAnswer.getAnswer().toString());
            }

            // 12. 提取就业方向
            StructuredUserDataReqVO.QuestionAnswer careerAnswer = userAnswers.get("12");
            if (careerAnswer != null && careerAnswer.getAnswer() != null) {
                profile.setCareerDirection(careerAnswer.getAnswer().toString());
            }

            // 14. 提取毕业去向
            StructuredUserDataReqVO.QuestionAnswer graduationAnswer = userAnswers.get("14");
            if (graduationAnswer != null && graduationAnswer.getAnswer() != null) {
                profile.setGraduationPlan(graduationAnswer.getAnswer().toString());
            }

            // 15. 提取就读城市省份
            StructuredUserDataReqVO.QuestionAnswer locationAnswer = userAnswers.get("15");
            if (locationAnswer != null && locationAnswer.getAnswer() != null) {
                profile.setPreferredLocation(locationAnswer.getAnswer().toString());
            }

            // 设置年份为2024（默认值）
            profile.setYear(2024);

            // 根据选科确定用户类型
            if (profile.getSubjects() != null && !profile.getSubjects().isEmpty()) {
                boolean hasPhysics = profile.getSubjects().stream().anyMatch(s -> s.contains("物理"));
                boolean hasHistory = profile.getSubjects().stream().anyMatch(s -> s.contains("历史"));

                String firstSubject = null;
                if (hasPhysics) {
                    firstSubject = "物理";
                } else if (hasHistory) {
                    firstSubject = "历史";
                }

                if (firstSubject != null) {
                    String typeName = determineSubjectSelection(profile.getProvince(), firstSubject);
                    profile.setTypeName(typeName);
                    log.info("根据用户选科确定类型为: {}", typeName);
                }
            }

        } catch (Exception e) {
            log.error("解析结构化用户数据失败", e);
        }

        return profile;
    }

    /**
     * 解析各科分数字符串
     * @param scoresStr 分数字符串，格式如："语文:88,数学:99,英语:88,历史:99,生物:99,政治:65"
     * @return 各科分数Map
     */
    private Map<String, Integer> parseSubjectScores(String scoresStr) {
        Map<String, Integer> subjectScores = new HashMap<>();
        if (StringUtils.hasText(scoresStr)) {
            String[] scoreItems = scoresStr.split(",");
            for (String item : scoreItems) {
                String[] parts = item.split(":");
                if (parts.length == 2) {
                    try {
                        String subject = parts[0].trim();
                        Integer score = Integer.parseInt(parts[1].trim());
                        subjectScores.put(subject, score);
                    } catch (NumberFormatException e) {
                        log.warn("解析科目分数失败: {}", item);
                    }
                }
            }
        }
        return subjectScores;
    }

    /**
     * 根据省份和首选科目确定科目选择类型
     *
     * @param province     省份
     * @param secondSubjcet 首选科目
     * @return 科目选择类型
     */
    private String determineSubjectSelection(String province, String secondSubjcet) {
        // 获取当前年份
        String year = String.valueOf(java.time.Year.now().getValue());

        // 使用SubjectSelectionUtils工具类获取科目选择类型
        return SubjectSelectionUtils.getSubjectSelectionType(province, year, secondSubjcet);
    }

    /**
     * 根据用户个人信息推荐专业（优化版本）
     * @param profile 用户个人信息
     * @return 推荐的专业列表和推荐理由
     */
    public Map<String, Object> recommendMajors(UserProfileInfo profile) {
        long startTime = System.currentTimeMillis();
        log.info("开始专业推荐，用户省份：{}，分数：{}，选科：{}",
                profile.getProvince(), profile.getTotalScore(), profile.getSubjects());

        Map<String, Object> result = new HashMap<>();
        List<MajorAdmissionInfo> higherScoreMajors = new ArrayList<>();
        List<MajorAdmissionInfo> equalScoreMajors = new ArrayList<>();
        List<MajorAdmissionInfo> lowerScoreMajors = new ArrayList<>();
        StringBuilder recommendationReason = new StringBuilder();
        int totalCount = 0;

        // 创建缓存Map，用于存储已查询过的历史数据
        Map<String, List<MajorAdmissionInfo.HistoricalYearData>> historicalDataCache = new HashMap<>();

        // 构建推荐理由开头
        buildRecommendationReasonHeader(profile, recommendationReason);

        try {
            // 优化1：一次性查询所有基础数据，避免重复查询
            List<MajorAdmissionInfo> allCandidateMajors = queryAllCandidateMajors(profile);
            log.info("一次性查询到 {} 条候选专业数据", allCandidateMajors.size());

            if (allCandidateMajors.isEmpty()) {
                return buildEmptyResult(profile, recommendationReason);
            }

            // 优化2：批量过滤和匹配专业
            List<MajorAdmissionInfo> matchedMajors = filterAndMatchMajors(allCandidateMajors, profile);
            log.info("过滤匹配后得到 {} 条专业数据", matchedMajors.size());

            // 优化3：批量查询招生计划数据
            List<MajorAdmissionInfo> majorsWithEnrollmentPlan = batchQueryEnrollmentPlans(matchedMajors, profile);
            log.info("有招生计划的专业数量: {}", majorsWithEnrollmentPlan.size());

            if (majorsWithEnrollmentPlan.isEmpty()) {
                return buildEmptyResult(profile, recommendationReason);
            }

            // 分类处理专业
            classifyMajorsByScore(majorsWithEnrollmentPlan, profile.getTotalScore(),
                    higherScoreMajors, equalScoreMajors, lowerScoreMajors);
            totalCount = majorsWithEnrollmentPlan.size();

            // 构建推荐理由
            buildRecommendationReasonContent(profile, majorsWithEnrollmentPlan, recommendationReason);

            // 优化4：异步查询历史数据，不阻塞主流程
            asyncFetchHistoricalData(higherScoreMajors, equalScoreMajors, lowerScoreMajors, historicalDataCache);

        } catch (Exception e) {
            log.error("专业推荐过程中发生异常", e);
            return buildErrorResult(e.getMessage());
        }

        // 构建最终结果
        result.put("higherScoreMajors", higherScoreMajors);
        result.put("equalScoreMajors", equalScoreMajors);
        result.put("lowerScoreMajors", lowerScoreMajors);
        result.put("totalCount", totalCount);
        result.put("recommendationReason", recommendationReason.toString());

        // 添加分类说明
        if (totalCount > 0) {
            addCategoryExplanation(recommendationReason, higherScoreMajors, equalScoreMajors, lowerScoreMajors);
        }

        long endTime = System.currentTimeMillis();
        log.info("专业推荐完成，耗时：{}ms，推荐专业总数：{}", endTime - startTime, totalCount);

        return result;
    }

    /**
     * 构建推荐理由开头
     */
    private void buildRecommendationReasonHeader(UserProfileInfo profile, StringBuilder recommendationReason) {
        recommendationReason.append("根据您的个人情况，我们为您推荐以下专业：\n\n");
        recommendationReason.append("1. 您来自").append(profile.getProvince()).append("，");
        recommendationReason.append(profile.getYear()).append("年高考，");
        recommendationReason.append("选科为").append(String.join("、", profile.getSubjects())).append("，");
        recommendationReason.append("高考总分为").append(profile.getTotalScore()).append("分。\n");

        recommendationReason.append("2. 您的性格特点为").append(String.join("、", profile.getPersonalityTraits())).append("，");
        if ("就业".equals(profile.getGraduationPlan())) {
            recommendationReason.append("毕业后计划直接就业。\n");
        } else {
            recommendationReason.append("毕业后的计划是").append(profile.getGraduationPlan()).append("。\n");
        }

        recommendationReason.append("3. 您对").append(String.join("、", profile.getInterestedMajorCategories())).append("等专业类别感兴趣。\n\n");
    }

    /**
     * 优化1：一次性查询所有候选专业数据
     */
    private List<MajorAdmissionInfo> queryAllCandidateMajors(UserProfileInfo profile) {
        int minScore = profile.getTotalScore() - 50;
        int maxScore = profile.getTotalScore() + 10;

        List<MajorAdmissionDO> admissionDOList;
        if (profile.getTypeName() != null && !profile.getTypeName().isEmpty()) {
            // 一次性查询所有符合条件的基础数据
            admissionDOList = majorAdmissionService.getMajorAdmissionByProvinceYearScoreRangeAndType(
                    profile.getProvince(), 2024, minScore, maxScore, profile.getTypeName());
        } else {
            admissionDOList = majorAdmissionService.getMajorAdmissionByProvinceYearAndScoreRange(
                    profile.getProvince(), 2024, minScore, maxScore);
        }

        return convertToMajorAdmissionInfoList(admissionDOList);
    }

    /**
     * 优化2：批量过滤和匹配专业
     */
    private List<MajorAdmissionInfo> filterAndMatchMajors(List<MajorAdmissionInfo> allCandidateMajors, UserProfileInfo profile) {
        List<MajorAdmissionInfo> matchedMajors = new ArrayList<>();
        Set<String> interestedCategories = new HashSet<>(profile.getInterestedMajorCategories());

        for (MajorAdmissionInfo major : allCandidateMajors) {
            // 检查是否匹配感兴趣的专业类别
            boolean isMatched = false;
            for (String category : interestedCategories) {
                if (major.getMajorName() != null && major.getMajorName().contains(category)) {
                    isMatched = true;
                    break;
                }
                // 关键词匹配
                List<String> keywords = splitMajorNameToKeywords(category);
                for (String keyword : keywords) {
                    if (major.getMajorName() != null && major.getMajorName().contains(keyword)) {
                        isMatched = true;
                        break;
                    }
                }
                if (isMatched) {
                    break;
                }
            }

            if (isMatched) {
                matchedMajors.add(major);
            }
        }

        // 如果匹配的专业不足，则使用所有候选专业
//        if (matchedMajors.size() < 20) {
//            log.info("匹配的专业数量不足，使用所有候选专业");
//            matchedMajors = allCandidateMajors;
//        }

        // 根据学生选科筛选数据
        return filterBySubjectSelection(matchedMajors, profile);
    }

    /**
     * 根据学生选科筛选数据
     */
    private List<MajorAdmissionInfo> filterBySubjectSelection(List<MajorAdmissionInfo> majors, UserProfileInfo profile) {
        if (profile.getSubjects() == null || profile.getSubjects().isEmpty()) {
            return majors;
        }

        Set<String> userSubjects = new HashSet<>(profile.getSubjects());
        log.info("用户选科信息: {}", userSubjects);

        List<MajorAdmissionInfo> filteredBySubjects = new ArrayList<>();
        for (MajorAdmissionInfo info : majors) {
            // 如果专业有选科要求，判断是否符合选科要求
            if (info.getSubjectSelection() != null && !info.getSubjectSelection().isEmpty()) {
                // 解析选科要求，例如"首选物理，再选化学"
                Set<String> requiredSubjects = parseSubjectSelection(info.getSubjectSelection());

                // 计算用户选科与专业要求的交集
                Set<String> intersection = new HashSet<>(userSubjects);
                intersection.retainAll(requiredSubjects);

                if (intersection.isEmpty() && !requiredSubjects.isEmpty()) {
                    // 如果交集为空且专业有选科要求，跳过该专业
                    log.debug("跳过专业 {} 因为用户选科 {} 与要求的科目 {} 不匹配",
                            info.getMajorName(), userSubjects, requiredSubjects);
                    continue;
                }
            }
            filteredBySubjects.add(info);
        }

        log.info("选科筛选后的专业数量: {}", filteredBySubjects.size());
        return filteredBySubjects;
    }

    /**
     * 优化3：批量查询招生计划数据
     */
    private List<MajorAdmissionInfo> batchQueryEnrollmentPlans(List<MajorAdmissionInfo> majors, UserProfileInfo profile) {
        List<MajorAdmissionInfo> majorsWithEnrollmentPlan = new ArrayList<>();

        // 限制查询数量，避免过多API调用
        int maxQueryCount = Math.min(majors.size(), 100);
        List<MajorAdmissionInfo> limitedMajors = majors.subList(0, maxQueryCount);

        for (MajorAdmissionInfo info : limitedMajors) {
            try {
                // 查询该专业的招生计划数据
                List<CollegeEnrollmentPlanInfo> enrollmentPlanData = queryEnrollmentPlanForMajor(info, profile);

                if (enrollmentPlanData != null && !enrollmentPlanData.isEmpty()) {
                    // 如果有招生计划数据，设置到专业信息中并加入推荐列表
                    info.setEnrollmentPlanData(enrollmentPlanData);
                    majorsWithEnrollmentPlan.add(info);
                    log.debug("专业 {} - {} 有招生计划数据，招生计划数量: {}",
                            info.getMajorName(), info.getSchoolName(), enrollmentPlanData.size());
                } else {
                    // 如果没有招生计划数据，不加入推荐列表
                    log.debug("专业 {} - {} 没有招生计划数据，已过滤", info.getMajorName(), info.getSchoolName());
                }
            } catch (Exception e) {
                log.warn("查询专业 {} - {} 的招生计划数据失败: {}",
                        info.getMajorName(), info.getSchoolName(), e.getMessage());
            }
        }

        return majorsWithEnrollmentPlan;
    }

    /**
     * 构建推荐理由内容
     */
    private void buildRecommendationReasonContent(UserProfileInfo profile, List<MajorAdmissionInfo> majorsWithEnrollmentPlan, StringBuilder recommendationReason) {
        recommendationReason.append("\n我们为您找到了").append(majorsWithEnrollmentPlan.size()).append("个符合您条件的院校和专业。");
        recommendationReason.append("这些专业的录取分数线与您的分数相匹配，并且符合您的选科要求。\n");

        // 列出前5个院校和专业
        int count = Math.min(5, majorsWithEnrollmentPlan.size());
        for (int i = 0; i < count; i++) {
            MajorAdmissionInfo info = majorsWithEnrollmentPlan.get(i);
            recommendationReason.append("- ").append(info.getSchoolName()).append(" ");
            recommendationReason.append(info.getMajorName()).append("，");
            recommendationReason.append("录取分数线：").append(info.getLowestScore()).append("\n");
        }

        recommendationReason.append("\n总结：以上推荐的专业符合您的兴趣方向，并且考虑了您的分数和选科情况。");
        recommendationReason.append("这些专业在就业市场有较好的前景，适合您的性格特点和职业规划。");
        recommendationReason.append("建议您进一步了解这些院校和专业的详细信息，以做出更好的选择。");
    }

    /**
     * 构建空结果
     */
    private Map<String, Object> buildEmptyResult(UserProfileInfo profile, StringBuilder recommendationReason) {
        Map<String, Object> result = new HashMap<>();

        recommendationReason.append("\n根据您的兴趣专业类别，我们没有找到完全匹配的专业。");
        recommendationReason.append("\n建议您尝试以下方法：");
        recommendationReason.append("\n1. 提供更多感兴趣的专业类别");
        recommendationReason.append("\n2. 调整您的分数范围");
        recommendationReason.append("\n3. 考虑其他省份的院校");
        recommendationReason.append("\n4. 联系我们的客服获取更多帮助");

        result.put("higherScoreMajors", new ArrayList<>());
        result.put("equalScoreMajors", new ArrayList<>());
        result.put("lowerScoreMajors", new ArrayList<>());
        result.put("totalCount", 0);
        result.put("recommendationReason", recommendationReason.toString());
        return result;
    }

    /**
     * 构建错误结果
     */
    private Map<String, Object> buildErrorResult(String errorMessage) {
        Map<String, Object> result = new HashMap<>();
        result.put("higherScoreMajors", new ArrayList<>());
        result.put("equalScoreMajors", new ArrayList<>());
        result.put("lowerScoreMajors", new ArrayList<>());
        result.put("totalCount", 0);
        result.put("recommendationReason", "推荐过程中发生错误：" + errorMessage);
        return result;
    }

    /**
     * 优化4：异步查询历史数据
     */
    private void asyncFetchHistoricalData(List<MajorAdmissionInfo> higherScoreMajors,
                                         List<MajorAdmissionInfo> equalScoreMajors,
                                         List<MajorAdmissionInfo> lowerScoreMajors,
                                         Map<String, List<MajorAdmissionInfo.HistoricalYearData>> historicalDataCache) {
        // 使用异步方式查询历史数据，不阻塞主流程
        CompletableFuture.runAsync(() -> {
            try {
                fetchHistoricalDataForMajors(higherScoreMajors, historicalDataCache);
                fetchHistoricalDataForMajors(equalScoreMajors, historicalDataCache);
                fetchHistoricalDataForMajors(lowerScoreMajors, historicalDataCache);
                log.info("历史数据查询完成");
            } catch (Exception e) {
                log.warn("异步查询历史数据失败", e);
            }
        });
    }

    /**
     * 添加分类说明
     */
    private void addCategoryExplanation(StringBuilder recommendationReason,
                                       List<MajorAdmissionInfo> higherScoreMajors,
                                       List<MajorAdmissionInfo> equalScoreMajors,
                                       List<MajorAdmissionInfo> lowerScoreMajors) {
        recommendationReason.append("\n\n我们将推荐的专业分为三类（每类仅显示分数最接近的专业）：\n");
        recommendationReason.append("1. 分数比您高的专业：").append(higherScoreMajors.size()).append("个\n");
        recommendationReason.append("2. 分数与您相近的专业：").append(equalScoreMajors.size()).append("个\n");
        recommendationReason.append("3. 分数比您低的专业：").append(lowerScoreMajors.size()).append("个\n");
        recommendationReason.append("您可以根据自己的实际情况和偏好选择适合的专业。");
    }

    /**
     * 根据分数差异将专业分类
     * @param majors 专业列表
     * @param userScore 用户分数
     * @param higherScoreMajors 分数比用户高的专业列表
     * @param equalScoreMajors 分数与用户相近的专业列表
     * @param lowerScoreMajors 分数比用户低的专业列表
     */
    private void classifyMajorsByScore(List<MajorAdmissionInfo> majors, Integer userScore,
                                      List<MajorAdmissionInfo> higherScoreMajors,
                                      List<MajorAdmissionInfo> equalScoreMajors,
                                      List<MajorAdmissionInfo> lowerScoreMajors) {
        if (majors == null || majors.isEmpty() || userScore == null) {
            return;
        }

        // 定义equal分类的目标数量，增加到10个
        final int TARGET_EQUAL_MAJORS = 50;

        // 创建一个列表来存储所有专业及其分数差异
        List<MajorWithScoreDiff> majorsWithDiff = new ArrayList<>();
        for (MajorAdmissionInfo major : majors) {
            Integer majorScore = Integer.valueOf(major.getLowestScore());
            if (majorScore == null) {
                continue;
            }
            int scoreDiff = majorScore - userScore;
            majorsWithDiff.add(new MajorWithScoreDiff(major, scoreDiff));
        }

        // 按分数差异的绝对值排序
        majorsWithDiff.sort(Comparator.comparingInt(m -> Math.abs(m.scoreDiff)));

        // 第一步：优先匹配equal类别的专业
        // 从分数差异最小的开始，逐步扩大范围直到找到足够的专业
        int rangeSize = 5; // 初始范围为±3分
        Set<MajorAdmissionInfo> processedMajors = new HashSet<>(); // 用于跟踪已处理的专业

        while (equalScoreMajors.size() < TARGET_EQUAL_MAJORS && rangeSize <= 20) { // 最大范围限制为±20分
            boolean foundNewMajors = false;

            for (MajorWithScoreDiff majorWithDiff : majorsWithDiff) {
                // 如果专业已经被处理过，跳过
                if (processedMajors.contains(majorWithDiff.major)) {
                    continue;
                }

                // 检查是否在当前范围内
                if (Math.abs(majorWithDiff.scoreDiff) <= rangeSize) {
                    equalScoreMajors.add(majorWithDiff.major);
                    processedMajors.add(majorWithDiff.major);
                    foundNewMajors = true;

                    // 如果已经找到足够的专业，退出循环
                    if (equalScoreMajors.size() >= TARGET_EQUAL_MAJORS) {
                        break;
                    }
                }
            }

            // 如果在当前范围内没有找到新的专业，或者已经处理了所有专业，扩大范围
            if (!foundNewMajors || processedMajors.size() >= majorsWithDiff.size()) {
                rangeSize++;
            } else if (equalScoreMajors.size() >= TARGET_EQUAL_MAJORS) {
                // 如果已经找到足够的专业，退出循环
                break;
            }
        }

        // 定义higher和lower分类的最大数量，增加到20个
        final int MAX_HIGHER_LOWER_MAJORS = 20;

        // 创建两个列表来分别存储higher和lower类别的专业
        List<MajorWithScoreDiff> higherMajors = new ArrayList<>();
        List<MajorWithScoreDiff> lowerMajors = new ArrayList<>();

        // 第二步：将剩余的专业分配到higher或lower类别的临时列表
        for (MajorWithScoreDiff majorWithDiff : majorsWithDiff) {
            // 如果专业已经被处理过，跳过
            if (processedMajors.contains(majorWithDiff.major)) {
                continue;
            }

            // 根据分数差异分配到higher或lower临时列表
            if (majorWithDiff.scoreDiff > 0) {
                higherMajors.add(majorWithDiff);
            } else {
                lowerMajors.add(majorWithDiff);
            }
        }

        // 对higher类别的专业按分数差异从小到大排序（更接近用户分数的排在前面）
        higherMajors.sort(Comparator.comparingInt(m -> m.scoreDiff));

        // 对lower类别的专业按分数差异的绝对值从小到大排序（更接近用户分数的排在前面）
        lowerMajors.sort(Comparator.comparingInt(m -> Math.abs(m.scoreDiff)));

        // 从排序后的列表中取出最多10个专业添加到结果列表
        for (int i = 0; i < Math.min(MAX_HIGHER_LOWER_MAJORS, higherMajors.size()); i++) {
            higherScoreMajors.add(higherMajors.get(i).major);
            processedMajors.add(higherMajors.get(i).major);
        }

        for (int i = 0; i < Math.min(MAX_HIGHER_LOWER_MAJORS, lowerMajors.size()); i++) {
            lowerScoreMajors.add(lowerMajors.get(i).major);
            processedMajors.add(lowerMajors.get(i).major);
        }
    }

    /**
     * 用于存储专业及其分数差异的辅助类
     */
    private static class MajorWithScoreDiff {
        MajorAdmissionInfo major;
        int scoreDiff;

        MajorWithScoreDiff(MajorAdmissionInfo major, int scoreDiff) {
            this.major = major;
            this.scoreDiff = scoreDiff;
        }
    }

    /**
     * 将专业名称拆分为关键词
     *
     * @param majorName 专业名称
     * @return 关键词列表
     */
    private List<String> splitMajorNameToKeywords(String majorName) {
        if (!StringUtils.hasText(majorName)) {
            return new ArrayList<>();
        }

        // 去除专业名称中的括号及其内容
        String cleanMajorName = majorName.replaceAll("\\([^)]*\\)", "").trim();

        // 使用中文分词工具类进行分词
        return ChineseSegmentationUtil.segment(cleanMajorName);
    }

    /**
     * 将用户选科列表转换为API所需的选科要求格式
     * @param subjects 用户选科列表
     * @return 符合API要求的选科格式
     */
    private String convertToSubjectSelectionFormat(List<String> subjects) {
        if (subjects == null || subjects.isEmpty()) {
            return "";
        }

        // 检查是否包含物理和历史，这两个是首选科目
        boolean hasPhysics = subjects.stream().anyMatch(s -> s.contains("物理"));
        boolean hasHistory = subjects.stream().anyMatch(s -> s.contains("历史"));

        // 如果既有物理又有历史，返回空字符串
        if (hasPhysics && hasHistory) {
            return "";
        }

        // 检查是否包含化学、生物、地理、政治（思想政治）
        boolean hasChemistry = subjects.stream().anyMatch(s -> s.contains("化学"));
        boolean hasBiology = subjects.stream().anyMatch(s -> s.contains("生物"));
        boolean hasGeography = subjects.stream().anyMatch(s -> s.contains("地理"));
        boolean hasPolitics = subjects.stream().anyMatch(s -> s.contains("政治") || s.contains("思想政治"));

        StringBuilder result = new StringBuilder();

        // 构建选科要求格式
        if (hasPhysics) {
            result.append("首选物理，再选");

            // 添加再选科目
            if (hasChemistry && !hasBiology && !hasGeography && !hasPolitics) {
                result.append("化学");
            } else if (!hasChemistry && hasBiology && !hasGeography && !hasPolitics) {
                result.append("生物");
            } else if (!hasChemistry && !hasBiology && hasGeography && !hasPolitics) {
                result.append("地理");
            } else if (!hasChemistry && !hasBiology && !hasGeography && hasPolitics) {
                result.append("思想政治");
            } else if (hasChemistry && hasBiology && !hasGeography && !hasPolitics) {
                result.append("化学/生物(2选1)");
            } else if (hasChemistry && !hasBiology && hasGeography && !hasPolitics) {
                result.append("化学/地理(2选1)");
            } else if (hasChemistry && !hasBiology && !hasGeography && hasPolitics) {
                result.append("化学/思想政治(2选1)");
            } else if (!hasChemistry && hasBiology && hasGeography && !hasPolitics) {
                result.append("生物/地理(2选1)");
            } else if (!hasChemistry && hasBiology && !hasGeography && hasPolitics) {
                result.append("生物/思想政治(2选1)");
            } else if (!hasChemistry && !hasBiology && hasGeography && hasPolitics) {
                result.append("地理/思想政治(2选1)");
            } else {
                result.append("");
            }
        } else if (hasHistory) {
            result.append("首选历史，再选");

            // 添加再选科目
            if (hasChemistry && !hasBiology && !hasGeography && !hasPolitics) {
                result.append("化学");
            } else if (!hasChemistry && hasBiology && !hasGeography && !hasPolitics) {
                result.append("生物");
            } else if (!hasChemistry && !hasBiology && hasGeography && !hasPolitics) {
                result.append("地理");
            } else if (!hasChemistry && !hasBiology && !hasGeography && hasPolitics) {
                result.append("思想政治");
            } else if (hasChemistry && hasBiology && !hasGeography && !hasPolitics) {
                result.append("化学/生物(2选1)");
            } else if (hasChemistry && !hasBiology && hasGeography && !hasPolitics) {
                result.append("化学/地理(2选1)");
            } else if (hasChemistry && !hasBiology && !hasGeography && hasPolitics) {
                result.append("化学/思想政治(2选1)");
            } else if (!hasChemistry && hasBiology && hasGeography && !hasPolitics) {
                result.append("生物/地理(2选1)");
            } else if (!hasChemistry && hasBiology && !hasGeography && hasPolitics) {
                result.append("生物/思想政治(2选1)");
            } else if (!hasChemistry && !hasBiology && hasGeography && hasPolitics) {
                result.append("地理/思想政治(2选1)");
            } else {
                result.append("");
            }
        } else {
            // 如果没有物理和历史，返回空字符串
            return "";
        }

        return result.toString();
    }

    /**
     * 解析选科要求字符串，例如"首选物理，再选化学"
     *
     * @param subjectSelection 选科要求字符串
     * @return 选科要求集合
     */
    private Set<String> parseSubjectSelection(String subjectSelection) {
        Set<String> subjects = new HashSet<>();
        if (subjectSelection == null || subjectSelection.isEmpty()) {
            return subjects;
        }

        // 处理首选科目
        if (subjectSelection.contains("首选")) {
            int firstStart = subjectSelection.indexOf("首选") + 2; // "首选"长度为2
            int firstEnd = subjectSelection.indexOf("，", firstStart); // 逗号分隔首选和再选
            if (firstEnd == -1) {
                // 如果没有逗号，可能只有首选科目
                firstEnd = subjectSelection.length();
            }
            String firstSubject = subjectSelection.substring(firstStart, firstEnd).trim();
            subjects.add(firstSubject);
        }

        // 处理再选科目
        if (subjectSelection.contains("再选")) {
            int secondStart = subjectSelection.indexOf("再选") + 2; // "再选"长度为2
            String secondPart = subjectSelection.substring(secondStart).trim();

            // 处理形如"化学/生物(2选1)"的格式
            if (secondPart.contains("/") && secondPart.contains("(2选1)")) {
                String[] options = secondPart.split("/");
                if (options.length >= 2) {
                    String option1 = options[0].trim();
                    String option2 = options[1].split("\\(")[0].trim();
                    subjects.add(option1);
                    subjects.add(option2);
                }
            } else {
                // 如果是单一科目
                subjects.add(secondPart);
            }
        }

        return subjects;
    }

    /**
     * 将MajorAdmissionDO列表转换为MajorAdmissionInfo列表
     * @param admissionDOList MajorAdmissionDO列表
     * @return MajorAdmissionInfo列表
     */
    private List<MajorAdmissionInfo> convertToMajorAdmissionInfoList(List<MajorAdmissionDO> admissionDOList) {
        if (admissionDOList == null || admissionDOList.isEmpty()) {
            return new ArrayList<>();
        }

        List<MajorAdmissionInfo> result = new ArrayList<>(admissionDOList.size());
        for (MajorAdmissionDO admissionDO : admissionDOList) {
            MajorAdmissionInfo info = new MajorAdmissionInfo();
            info.setProvinceName(admissionDO.getProvinceName());
            info.setSchoolUUID(admissionDO.getSchoolUuid());
            info.setSchoolName(admissionDO.getSchoolName());
            info.setMajorName(admissionDO.getMajorName());
            info.setMajorCode(admissionDO.getMajorCode());
            info.setYear(admissionDO.getYear());
            info.setHighScore(admissionDO.getHighScore());
            info.setAverageScore(admissionDO.getAverageScore());
            info.setLowestScore(admissionDO.getLowestScore());
            info.setLowestSection(admissionDO.getLowestSection());
            info.setBatchName(admissionDO.getBatchName());
            info.setTypeName(admissionDO.getTypeName());
            info.setProScore(admissionDO.getProScore());
            info.setSubjectSelection(admissionDO.getSubjectSelection());
            info.setMajorStandardCode(admissionDO.getMajorStandardCode());
            result.add(info);
        }
        return result;
    }

    /**
     * 将MajorAdmissionDO转换为MajorAdmissionInfo.HistoricalYearData
     * @param admissionDO MajorAdmissionDO
     * @return HistoricalYearData
     */
    private MajorAdmissionInfo.HistoricalYearData convertToHistoricalYearData(MajorAdmissionDO admissionDO) {
        if (admissionDO == null) {
            return null;
        }

        MajorAdmissionInfo.HistoricalYearData yearData = new MajorAdmissionInfo.HistoricalYearData();
        yearData.setYear(admissionDO.getYear());
        yearData.setHighScore(admissionDO.getHighScore());
        yearData.setAverageScore(admissionDO.getAverageScore());
        yearData.setLowestScore(admissionDO.getLowestScore());
        yearData.setLowestSection(admissionDO.getLowestSection());
        return yearData;
    }

    /**
     * 为指定的专业列表查询历史数据
     * @param majors 专业列表
     * @param historicalDataCache 历史数据缓存
     */
    private void fetchHistoricalDataForMajors(List<MajorAdmissionInfo> majors, Map<String, List<MajorAdmissionInfo.HistoricalYearData>> historicalDataCache) {
        if (majors == null || majors.isEmpty()) {
            return;
        }

        for (MajorAdmissionInfo admissionInfo : majors) {
            // 生成缓存键：学校UUID + 专业名称
            String cacheKey = admissionInfo.getSchoolUUID() + "_" + admissionInfo.getMajorName();

            // 检查缓存中是否已有该专业的历史数据
            if (historicalDataCache.containsKey(cacheKey)) {
                // 如果缓存中已有数据，直接使用
                admissionInfo.setHistoricalData(historicalDataCache.get(cacheKey));
                log.info("使用缓存数据: {} - {}", admissionInfo.getMajorName(), admissionInfo.getSchoolName());
            } else {
                // 缓存中没有数据，需要查询
                List<MajorAdmissionInfo.HistoricalYearData> historicalDataList = new ArrayList<>();

                // 使用多线程并行查询2021-2023年的历史数据
                List<Future<List<MajorAdmissionInfo.HistoricalYearData>>> futures = new ArrayList<>();
                CountDownLatch latch = new CountDownLatch(3); // 三个年份

                // 并行查询每个年份的数据
                for (int historyYear = 2021; historyYear <= 2023; historyYear++) {
                    final int year = historyYear; // 必须使用final变量传入lambda表达式

                    // 提交查询任务到线程池
                    Future<List<MajorAdmissionInfo.HistoricalYearData>> future = executorService.submit(() -> {
                        try {
                            log.info("正在查询{}年历史数据: {} - {}", year, admissionInfo.getMajorName(), admissionInfo.getSchoolName());

                            // 从数据库中查询历史数据
                            List<MajorAdmissionDO> historyDOList = majorAdmissionService.getMajorAdmissionBySchoolMajorYearTypeNameprovinceName(
                                    admissionInfo.getSchoolUUID(),
                                    admissionInfo.getMajorName(),
                                    year,admissionInfo.getTypeName(),admissionInfo.getProvinceName());

                            if (historyDOList != null && !historyDOList.isEmpty()) {
                                // 转换为历史数据对象列表
                                return historyDOList.stream()
                                        .map(this::convertToHistoricalYearData)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toList());
                            }

                            return Collections.emptyList();
                        } catch (Exception e) {
                            // 记录异常但继续处理
                            log.error("查询{}年历史数据失败: {}", year, e.getMessage());
                            return Collections.emptyList();
                        } finally {
                            latch.countDown(); // 无论成功失败，都计数-1
                        }
                    });

                    futures.add(future);
                }

                // 等待所有查询完成或超时
                try {
                    boolean completed = latch.await(10, TimeUnit.SECONDS);
                    if (!completed) {
                        log.warn("查询历史数据超时");
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("查询历史数据被中断", e);
                }

                // 收集查询结果
                // 使用Set来去除重复项
                Set<MajorAdmissionInfo.HistoricalYearData> uniqueDataSet = new HashSet<>();
                for (Future<List<MajorAdmissionInfo.HistoricalYearData>> future : futures) {
                    try {
                        List<MajorAdmissionInfo.HistoricalYearData> yearDataList = future.get(1, TimeUnit.SECONDS);
                        if (yearDataList != null && !yearDataList.isEmpty()) {
                            for (MajorAdmissionInfo.HistoricalYearData data : yearDataList) {
                                if (uniqueDataSet.add(data)) { // 如果成功添加到Set，说明不是重复项
                                    historicalDataList.add(data);
                                } else {
                                    log.debug("发现重复的历史数据，已跳过: 年份={}, 分数={}, 位次={}",
                                            data.getYear(), data.getLowestScore(), data.getLowestSection());
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("获取历史数据结果失败", e);
                    }
                }

                // 如果查询到了历史数据，则设置到当前专业对象中并缓存
                if (!historicalDataList.isEmpty()) {
                    admissionInfo.setHistoricalData(historicalDataList);
                    // 将历史数据添加到缓存中
                    historicalDataCache.put(cacheKey, historicalDataList);
                }
            }
        }
    }

    /**
     * 为单个专业查询招生计划数据（带缓存）
     * @param admissionInfo 专业信息
     * @param profile 用户个人信息
     * @return 招生计划数据列表，如果没有数据则返回null或空列表
     */
    private List<CollegeEnrollmentPlanInfo> queryEnrollmentPlanForMajor(MajorAdmissionInfo admissionInfo, UserProfileInfo profile) {
        try {
            // 设置招生计划数据加载状态
            admissionInfo.setEnrollmentPlanDataLoading(true);

            log.debug("正在查询招生计划数据: {} - {}", admissionInfo.getMajorName(), admissionInfo.getSchoolName());

            // 使用缓存服务查询招生计划数据
            List<CollegeEnrollmentPlanInfo> enrollmentPlanList = collegeEnrollmentPlanService.getEnrollmentPlanForMajor(
                    admissionInfo.getSchoolName(),
                    admissionInfo.getMajorName(),
                    profile.getProvince(),
                    profile.getTypeName(),
                    2024 // 查询2024年的招生计划数据
            );

            if (enrollmentPlanList != null && !enrollmentPlanList.isEmpty()) {
                log.debug("成功获取招生计划数据: {} - {}, 数据量: {}",
                        admissionInfo.getMajorName(), admissionInfo.getSchoolName(), enrollmentPlanList.size());
                return enrollmentPlanList;
            } else {
                log.debug("未找到招生计划数据: {} - {}", admissionInfo.getMajorName(), admissionInfo.getSchoolName());
                return null;
            }

        } catch (Exception e) {
            log.error("查询招生计划数据异常: {} - {}, 错误: {}",
                    admissionInfo.getMajorName(), admissionInfo.getSchoolName(), e.getMessage(), e);
            return null;
        } finally {
            // 无论成功失败，都设置加载状态为false
            admissionInfo.setEnrollmentPlanDataLoading(false);
        }
    }

    /**
     * 根据用户个人信息获取所有适合的专业（恢复兴趣匹配，取消招生计划匹配）
     * @param profile 用户个人信息
     * @return 所有适合的专业列表
     */
    public Map<String, Object> getAllSuitableMajors(UserProfileInfo profile) {
        long startTime = System.currentTimeMillis();
        log.info("开始获取所有适合专业，用户省份：{}，分数：{}，选科：{}，意向专业：{}",
                profile.getProvince(), profile.getTotalScore(), profile.getSubjects(), profile.getInterestedMajorCategories());

        Map<String, Object> result = new HashMap<>();
        StringBuilder queryInfo = new StringBuilder();
        List<MajorAdmissionInfo> rushMajors = new ArrayList<>();
        List<MajorAdmissionInfo> stableMajors = new ArrayList<>();
        List<MajorAdmissionInfo> safeMajors = new ArrayList<>();

        try {
            // 扩大分数范围，获取更多专业
            List<MajorAdmissionInfo> allCandidateMajors = queryAllCandidateMajorsWithExtendedRange(profile);
            log.info("扩大范围查询到 {} 条候选专业数据", allCandidateMajors.size());

            if (allCandidateMajors.isEmpty()) {
                return buildEmptyAllSuitableResult(profile, queryInfo);
            }

            // 进行兴趣匹配和选科筛选
            List<MajorAdmissionInfo> matchedMajors = filterAndMatchMajorsForAllSuitable(allCandidateMajors, profile);
            log.info("兴趣匹配和选科筛选后得到 {} 条专业数据", matchedMajors.size());

            if (matchedMajors.isEmpty()) {
                return buildEmptyAllSuitableResult(profile, queryInfo);
            }

            // 按冲稳保分类专业
            Map<String, List<MajorAdmissionInfo>> classifiedMajors = classifyMajorsByRushStableSafe(matchedMajors, profile.getTotalScore());

            rushMajors = classifiedMajors.get("rush");
            stableMajors = classifiedMajors.get("stable");
            safeMajors = classifiedMajors.get("safe");

            // 为最终分类结果查询近三年招生计划数据
            batchQueryThreeYearsEnrollmentPlans(rushMajors, profile);
            batchQueryThreeYearsEnrollmentPlans(stableMajors, profile);
            batchQueryThreeYearsEnrollmentPlans(safeMajors, profile);

            // 构建查询信息说明
            buildQueryInfoForAllSuitableWithClassification(profile, rushMajors, stableMajors, safeMajors, queryInfo);

        } catch (Exception e) {
            log.error("获取所有适合专业过程中发生异常", e);
            return buildErrorAllSuitableResult(e.getMessage());
        }

        result.put("rushMajors", rushMajors);
        result.put("stableMajors", stableMajors);
        result.put("safeMajors", safeMajors);
        result.put("rushCount", rushMajors.size());
        result.put("stableCount", stableMajors.size());
        result.put("safeCount", safeMajors.size());
        result.put("totalCount", rushMajors.size() + stableMajors.size() + safeMajors.size());
        result.put("queryInfo", queryInfo.toString());

        long endTime = System.currentTimeMillis();
        log.info("获取所有适合专业完成，耗时：{}ms，冲刺：{}个，稳妥：{}个，保底：{}个",
                endTime - startTime, rushMajors.size(), stableMajors.size(), safeMajors.size());

        return result;
    }

    /**
     * 扩大分数范围查询所有候选专业数据
     */
    private List<MajorAdmissionInfo> queryAllCandidateMajorsWithExtendedRange(UserProfileInfo profile) {
        // 扩大分数范围：下限-100分，上限+50分
        int minScore = profile.getTotalScore() - 100;
        int maxScore = profile.getTotalScore() + 50;

        List<MajorAdmissionDO> admissionDOList;
        if (profile.getTypeName() != null && !profile.getTypeName().isEmpty()) {
            admissionDOList = majorAdmissionService.getMajorAdmissionByProvinceYearScoreRangeAndType(
                    profile.getProvince(), 2024, minScore, maxScore, profile.getTypeName());
        } else {
            admissionDOList = majorAdmissionService.getMajorAdmissionByProvinceYearAndScoreRange(
                    profile.getProvince(), 2024, minScore, maxScore);
        }

        return convertToMajorAdmissionInfoList(admissionDOList);
    }

    /**
     * 为获取所有适合专业进行兴趣匹配和选科筛选（不限制数量）
     */
    private List<MajorAdmissionInfo> filterAndMatchMajorsForAllSuitable(List<MajorAdmissionInfo> allCandidateMajors, UserProfileInfo profile) {
        List<MajorAdmissionInfo> matchedMajors = new ArrayList<>();
        Set<String> interestedCategories = new HashSet<>();

        // 如果用户有意向专业，进行兴趣匹配
        if (profile.getInterestedMajorCategories() != null && !profile.getInterestedMajorCategories().isEmpty()) {
            interestedCategories.addAll(profile.getInterestedMajorCategories());

            for (MajorAdmissionInfo major : allCandidateMajors) {
                // 检查是否匹配感兴趣的专业类别
                boolean isMatched = false;
                for (String category : interestedCategories) {
                    if (major.getMajorName() != null && major.getMajorName().contains(category)) {
                        isMatched = true;
                        break;
                    }
                    // 关键词匹配
                    List<String> keywords = splitMajorNameToKeywords(category);
                    for (String keyword : keywords) {
                        if (major.getMajorName() != null && major.getMajorName().contains(keyword)) {
                            isMatched = true;
                            break;
                        }
                    }
                    if (isMatched) {
                        break;
                    }
                }

                if (isMatched) {
                    matchedMajors.add(major);
                }
            }

            log.info("兴趣匹配后的专业数量: {}", matchedMajors.size());
        } else {
            // 如果没有意向专业，返回所有候选专业
            matchedMajors = allCandidateMajors;
            log.info("用户未指定意向专业，返回所有候选专业: {}", matchedMajors.size());
        }

        // 根据学生选科筛选数据
        return filterBySubjectSelection(matchedMajors, profile);
    }

    /**
     * 按冲稳保分类专业
     * @param majors 专业列表
     * @param userScore 用户分数
     * @return 分类后的专业Map
     */
    private Map<String, List<MajorAdmissionInfo>> classifyMajorsByRushStableSafe(List<MajorAdmissionInfo> majors, int userScore) {
        Map<String, List<MajorAdmissionInfo>> result = new HashMap<>();
        List<MajorAdmissionInfo> rushMajors = new ArrayList<>();    // 冲刺：分数高于用户分数
        List<MajorAdmissionInfo> stableMajors = new ArrayList<>();  // 稳妥：分数接近用户分数
        List<MajorAdmissionInfo> safeMajors = new ArrayList<>();    // 保底：分数低于用户分数

        for (MajorAdmissionInfo major : majors) {
            try {
                int majorScore = Integer.parseInt(major.getLowestScore());
                int scoreDiff = majorScore - userScore;

                if (scoreDiff > 10) {
                    // 冲刺：专业分数比用户分数高10分以上
                    rushMajors.add(major);
                } else if (scoreDiff >= -10) {
                    // 稳妥：专业分数在用户分数±10分范围内
                    stableMajors.add(major);
                } else {
                    // 保底：专业分数比用户分数低10分以上
                    safeMajors.add(major);
                }
            } catch (NumberFormatException e) {
                // 无法解析分数的专业放入稳妥类别
                stableMajors.add(major);
                log.warn("无法解析专业分数: {} - {}", major.getSchoolName(), major.getMajorName());
            }
        }

        // 按分数排序
        Comparator<MajorAdmissionInfo> scoreComparator = (m1, m2) -> {
            try {
                int score1 = Integer.parseInt(m1.getLowestScore());
                int score2 = Integer.parseInt(m2.getLowestScore());
                return Integer.compare(score1, score2);
            } catch (NumberFormatException e) {
                return 0;
            }
        };

        rushMajors.sort(scoreComparator);
        stableMajors.sort(scoreComparator);
        safeMajors.sort(scoreComparator);

        result.put("rush", rushMajors);
        result.put("stable", stableMajors);
        result.put("safe", safeMajors);

        log.info("专业分类完成 - 冲刺：{}个，稳妥：{}个，保底：{}个",
                rushMajors.size(), stableMajors.size(), safeMajors.size());

        return result;
    }

    /**
     * 构建查询信息说明（用于getAllSuitableMajors，包含分类信息）
     */
    private void buildQueryInfoForAllSuitableWithClassification(UserProfileInfo profile,
            List<MajorAdmissionInfo> rushMajors, List<MajorAdmissionInfo> stableMajors,
            List<MajorAdmissionInfo> safeMajors, StringBuilder queryInfo) {

        queryInfo.append("查询条件：\n");
        queryInfo.append("• 省份：").append(profile.getProvince()).append("\n");
        queryInfo.append("• 年份：").append(profile.getYear()).append("\n");
        queryInfo.append("• 选科：").append(String.join("、", profile.getSubjects())).append("\n");
        queryInfo.append("• 分数：").append(profile.getTotalScore()).append("分\n");
        queryInfo.append("• 分数范围：").append(profile.getTotalScore() - 100).append("-").append(profile.getTotalScore() + 50).append("分\n");

        if (profile.getInterestedMajorCategories() != null && !profile.getInterestedMajorCategories().isEmpty()) {
            queryInfo.append("• 意向专业：").append(String.join("、", profile.getInterestedMajorCategories())).append("\n");
        }
        queryInfo.append("\n");

        queryInfo.append("查询结果（按冲稳保分类）：\n");
        queryInfo.append("• 冲刺专业：").append(rushMajors.size()).append("个（分数高于").append(profile.getTotalScore() + 10).append("分）\n");
        queryInfo.append("• 稳妥专业：").append(stableMajors.size()).append("个（分数在").append(profile.getTotalScore() - 10).append("-").append(profile.getTotalScore() + 10).append("分范围内）\n");
        queryInfo.append("• 保底专业：").append(safeMajors.size()).append("个（分数低于").append(profile.getTotalScore() - 10).append("分）\n");
        queryInfo.append("• 专业总数：").append(rushMajors.size() + stableMajors.size() + safeMajors.size()).append("个\n");

        if (profile.getInterestedMajorCategories() != null && !profile.getInterestedMajorCategories().isEmpty()) {
            queryInfo.append("• 所有专业均符合您的意向专业要求\n");
        }
        queryInfo.append("• 所有专业均符合您的选科要求\n");
        queryInfo.append("• 未进行招生计划筛选\n\n");

        // 分类分数范围统计
        if (!rushMajors.isEmpty()) {
            MajorAdmissionInfo rushLowest = rushMajors.get(0);
            MajorAdmissionInfo rushHighest = rushMajors.get(rushMajors.size() - 1);
            queryInfo.append("冲刺专业分数范围：").append(rushLowest.getLowestScore()).append("-").append(rushHighest.getLowestScore()).append("分\n");
        }

        if (!stableMajors.isEmpty()) {
            MajorAdmissionInfo stableLowest = stableMajors.get(0);
            MajorAdmissionInfo stableHighest = stableMajors.get(stableMajors.size() - 1);
            queryInfo.append("稳妥专业分数范围：").append(stableLowest.getLowestScore()).append("-").append(stableHighest.getLowestScore()).append("分\n");
        }

        if (!safeMajors.isEmpty()) {
            MajorAdmissionInfo safeLowest = safeMajors.get(0);
            MajorAdmissionInfo safeHighest = safeMajors.get(safeMajors.size() - 1);
            queryInfo.append("保底专业分数范围：").append(safeLowest.getLowestScore()).append("-").append(safeHighest.getLowestScore()).append("分\n");
        }
    }

    /**
     * 构建查询信息说明（用于getAllSuitableMajors）
     */
    private void buildQueryInfoForAllSuitable(UserProfileInfo profile, List<MajorAdmissionInfo> allSuitableMajors, StringBuilder queryInfo) {
        queryInfo.append("查询条件：\n");
        queryInfo.append("• 省份：").append(profile.getProvince()).append("\n");
        queryInfo.append("• 年份：").append(profile.getYear()).append("\n");
        queryInfo.append("• 选科：").append(String.join("、", profile.getSubjects())).append("\n");
        queryInfo.append("• 分数：").append(profile.getTotalScore()).append("分\n");
        queryInfo.append("• 分数范围：").append(profile.getTotalScore() - 100).append("-").append(profile.getTotalScore() + 50).append("分\n");

        if (profile.getInterestedMajorCategories() != null && !profile.getInterestedMajorCategories().isEmpty()) {
            queryInfo.append("• 意向专业：").append(String.join("、", profile.getInterestedMajorCategories())).append("\n");
        }
        queryInfo.append("\n");

        queryInfo.append("查询结果：\n");
        queryInfo.append("• 共找到 ").append(allSuitableMajors.size()).append(" 个符合条件的专业\n");

        if (profile.getInterestedMajorCategories() != null && !profile.getInterestedMajorCategories().isEmpty()) {
            queryInfo.append("• 所有专业均符合您的意向专业要求\n");
        }
        queryInfo.append("• 所有专业均符合您的选科要求\n");
        queryInfo.append("• 专业按录取分数线从低到高排序\n");
        queryInfo.append("• 未进行招生计划筛选\n\n");

        if (!allSuitableMajors.isEmpty()) {
            MajorAdmissionInfo lowest = allSuitableMajors.get(0);
            MajorAdmissionInfo highest = allSuitableMajors.get(allSuitableMajors.size() - 1);
            queryInfo.append("分数范围：\n");
            queryInfo.append("• 最低分：").append(lowest.getLowestScore()).append("分（").append(lowest.getSchoolName()).append(" - ").append(lowest.getMajorName()).append("）\n");
            queryInfo.append("• 最高分：").append(highest.getLowestScore()).append("分（").append(highest.getSchoolName()).append(" - ").append(highest.getMajorName()).append("）\n");
        }
    }

    /**
     * 构建查询信息说明（原方法保留）
     */
    private void buildQueryInfo(UserProfileInfo profile, List<MajorAdmissionInfo> allSuitableMajors, StringBuilder queryInfo) {
        queryInfo.append("查询条件：\n");
        queryInfo.append("• 省份：").append(profile.getProvince()).append("\n");
        queryInfo.append("• 年份：").append(profile.getYear()).append("\n");
        queryInfo.append("• 选科：").append(String.join("、", profile.getSubjects())).append("\n");
        queryInfo.append("• 分数：").append(profile.getTotalScore()).append("分\n");
        queryInfo.append("• 分数范围：").append(profile.getTotalScore() - 100).append("-").append(profile.getTotalScore() + 50).append("分\n\n");

        queryInfo.append("查询结果：\n");
        queryInfo.append("• 共找到 ").append(allSuitableMajors.size()).append(" 个符合条件的专业\n");
        queryInfo.append("• 所有专业均有招生计划数据\n");
        queryInfo.append("• 所有专业均符合您的选科要求\n");
        queryInfo.append("• 专业按录取分数线从低到高排序\n\n");

        if (!allSuitableMajors.isEmpty()) {
            MajorAdmissionInfo lowest = allSuitableMajors.get(0);
            MajorAdmissionInfo highest = allSuitableMajors.get(allSuitableMajors.size() - 1);
            queryInfo.append("分数范围：\n");
            queryInfo.append("• 最低分：").append(lowest.getLowestScore()).append("分（").append(lowest.getSchoolName()).append(" - ").append(lowest.getMajorName()).append("）\n");
            queryInfo.append("• 最高分：").append(highest.getLowestScore()).append("分（").append(highest.getSchoolName()).append(" - ").append(highest.getMajorName()).append("）\n");
        }
    }

    /**
     * 构建空的所有适合专业结果（冲稳保分类）
     */
    private Map<String, Object> buildEmptyAllSuitableResult(UserProfileInfo profile, StringBuilder queryInfo) {
        Map<String, Object> result = new HashMap<>();

        queryInfo.append("查询条件：\n");
        queryInfo.append("• 省份：").append(profile.getProvince()).append("\n");
        queryInfo.append("• 年份：").append(profile.getYear()).append("\n");
        queryInfo.append("• 选科：").append(String.join("、", profile.getSubjects())).append("\n");
        queryInfo.append("• 分数：").append(profile.getTotalScore()).append("分\n\n");

        queryInfo.append("查询结果：\n");
        queryInfo.append("• 未找到符合条件的专业\n");
        queryInfo.append("• 建议调整查询条件或联系客服获取帮助\n");

        result.put("rushMajors", new ArrayList<>());
        result.put("stableMajors", new ArrayList<>());
        result.put("safeMajors", new ArrayList<>());
        result.put("rushCount", 0);
        result.put("stableCount", 0);
        result.put("safeCount", 0);
        result.put("totalCount", 0);
        result.put("queryInfo", queryInfo.toString());
        return result;
    }

    /**
     * 构建错误的所有适合专业结果（冲稳保分类）
     */
    private Map<String, Object> buildErrorAllSuitableResult(String errorMessage) {
        Map<String, Object> result = new HashMap<>();
        result.put("rushMajors", new ArrayList<>());
        result.put("stableMajors", new ArrayList<>());
        result.put("safeMajors", new ArrayList<>());
        result.put("rushCount", 0);
        result.put("stableCount", 0);
        result.put("safeCount", 0);
        result.put("totalCount", 0);
        result.put("queryInfo", "查询过程中发生错误：" + errorMessage);
        return result;
    }
}
